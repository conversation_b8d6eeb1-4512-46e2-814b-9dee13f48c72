﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Principale.Master" AutoEventWireup="true"
    CodeBehind="statistiques_paniers.aspx.cs" Inherits="login.pages_stats.statistiques_paniers"
    Culture="auto" meta:resourcekey="PageResource1" UICulture="auto" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <div class="col-lg-4 col-lg-offset-2">
        <div class="form-horizontal">
            <div class="form-group">
                <label class="col-lg-3 control-label" for="fromDate" ID="lblFromDate" data-trad="date_debut"></label>
                <div class="controls">
                    <input type="text" id="fromDate" />
                </div>
            </div>
            <div class="form-group">
        
                <label class="col-lg-3 control-label" for="transactionID" ID="lblTransactionID" data-trad="transaction_id"></label>
                <div class="controls">
                    <input type="text" id="transactionID" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg-3 control-label" for="customerID" ID="lblCustomerID" data-trad="customer_number"></label>               
                <div class="controls">
                    <input type="text" id="customerID" />
                </div>
            </div>
            <div class="form-group">              
                <label class="col-lg-3 control-label" for="email" ID="lblEmail" data-trad="email"></label>
                <div class="controls">
                    <input type="text" id="emailAdress" />
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="form-horizontal">
            <div class="form-group">
                <label class="col-lg-3 control-label" for="toDate" ID="lblToDate" data-trad="date_fin"></label>               
                <div class="controls">
                    <input type="text" id="toDate" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg-3 control-label" for="orderID" ID="lblOrderID" data-trad="order_id"></label>              
                <div class="controls">
                    <input type="text" id="orderID" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg-3 control-label" for="basketState" ID="lblBasketState" data-trad="basket_state"></label>                 
                <div class="controls">
                    <select id="basketState" class="selectpicker">
                        <option Value="T" data-trad="etat_tous" selected></option>
                        <option Value="V" data-trad="valider" ></option>
                        <option Value="P" data-trad="etat_payer" ></option>
                        <option Value="R" data-trad="etat_editer" ></option>
                        <option Value="I" data-trad="etat_invalide" ></option>
                        <option Value="C" data-trad="etat_cree" ></option>
                    </select>



                    <%--<asp:DropDownList CssClass="selectpicker" data-style="btn-primary success" ID="basketState"
                        runat="server" meta:resourcekey="basketStateResource1">
                        <asp:ListItem Value="T" Selected="True" Text="Tous" meta:resourcekey="ListItemResource1" />
                        <asp:ListItem Value="V" Text="Validé" meta:resourcekey="ListItemResource2" />
                        <asp:ListItem Value="P" Text="Payé" meta:resourcekey="ListItemResource3" />
                        <asp:ListItem Value="R" Text="Réservé" meta:resourcekey="ListItemResource4" />
                        <asp:ListItem Value="I" Text="Invalide" meta:resourcekey="ListItemResource5" />
                        <asp:ListItem Value="C" Text="Créé" meta:resourcekey="ListItemResource6" />
                    </asp:DropDownList>--%>
                </div>
            </div>
        </div>
    </div>  
    
      <div class="col-lg col-lg-12">
        <div id="error">
            <asp:Label ID="lblErrorDate" CssClass="hidden" Text="" runat="server" meta:resourcekey="lblErrorDateResource1"></asp:Label>
            <asp:Label ID="lblError" CssClass="hidden" runat="server" meta:resourcekey="lblErrorResource1"></asp:Label>
        </div>
    </div>

<%--    <div class="col-lg-9 col-lg-offset-2 ">
        <button class="btn btn-lg btn-primary" id="btnSearch" type="button">
                <i class="icon-ok"></i>
                <span data-trad="valider"></span></button>
      
       <button class="btn btn-lg btn-success" id="pdfExport" type="button">
                <i class="icon-ok"></i>
                <span data-trad="export_pdf"></span></button>
      
    </div>--%>


  
        <div class="col-sm-9 "><button class="btn btn-lg btn-primary" id="btnSearch" type="button"> <i class="icon-ok"></i><span  data-trad="valider"> </span></button></div>
         <div class="col-sm-3"><button class="btn btn-lg btn-success" id="pdfExport" type="button"> <i class="icon-upload"></i><span data-trad="export_pdf"></span></button></div>
  

    <div class="col-lg col-lg-12">
        <div id="datatableResult" class="table-responsive">
            <table id="tableSearch" width="100%" class="table table-striped table-bordered table-hover dataTable">
                <thead>
                    <tr>
                        <th>
                            <label data-trad="detail"></label>
                        </th>
                        <th> <label data-trad="web_user_id"></label> </th>
                        <th> <label data-trad="identite_id"></label> </th>
                        <th> <label data-trad="basket_id"></label> </th>
                        <th> <label data-trad="structure_id"></label></th>
                        <th> <label data-trad="date_operation"></label></th>
                        <th> <label data-trad="status"></label></th>
                        <th> <label data-trad="order_id"></label></th>
                        <th> <label data-trad="transaction_id"></label></th>
                        <th> <label data-trad="certificate"></label></th>
                        <th> <label data-trad="pay_request"></label></th>
                        <th> <label data-trad="card_number"></label></th>
                        <th> <label data-trad="card_type"></label></th>
                        <th> <label data-trad="email"></label></th>
                        <th> <label data-trad="paiement_date"></label></th>
                        <th> <label data-trad="print_home" id="lblAsPdf"></label></th>
                        <th> <label data-trad="numero_billets"></label></th>


                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <iframe id="iframe_forDownloadPdf" style="display: none;"></iframe>
    <script src="../assets/js/date-time/jquery.datetimepicker.js" type="text/javascript"></script>
    <script src="../assets/js/date-time/dataTimePickerCustom.js" type="text/javascript"></script>
    <%--<script src="../assets/js/jquery.dataTables.min.js" type="text/javascript"></script>--%>
    <script src="../assets/js/jquery.dataTables.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.dataTables.bootstrap.js" type="text/javascript"></script>
    <%--<script src="../assets/media/js/ZeroClipboard.js" type="text/javascript"></script>--%>
    <script src="../assets/media/js/TableTools.js" type="text/javascript"></script>
    <script src="../assets/js/pages/statistiques_paniers.js" type="text/javascript"></script>
</asp:Content>
